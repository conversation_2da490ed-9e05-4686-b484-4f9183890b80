<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require user view permissions
requirePermission($mysqli, 'users.view');

$msg = "";
$error = "";

// Handle user deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_user'])) {
  $user_id = intval($_POST['user_id']);
  
  if ($user_id && hasPermission($mysqli, $_SESSION['user_id'], 'users.delete')) {
    // Don't allow deleting yourself
    if ($user_id === $_SESSION['user_id']) {
      $error = "You cannot delete your own account.";
    } else {
      $mysqli->begin_transaction();
      
      try {
        // Delete user roles first
        $roles_stmt = $mysqli->prepare("DELETE FROM user_roles WHERE user_id = ?");
        $roles_stmt->bind_param('i', $user_id);
        $roles_stmt->execute();
        $roles_stmt->close();

        // Delete user account
        $user_stmt = $mysqli->prepare("DELETE FROM users WHERE id = ?");
        $user_stmt->bind_param('i', $user_id);
        
        if ($user_stmt->execute()) {
          $user_stmt->close();
          $mysqli->commit();
          $msg = "User account deleted successfully.";
        } else {
          throw new Exception("Failed to delete user account.");
        }
      } catch (Exception $e) {
        $mysqli->rollback();
        $error = $e->getMessage();
      }
    }
  }
}

// Fetch all users with employee information
$users_query = "
  SELECT u.*, e.name as employee_name, e.email as employee_email, e.department, e.position,
         GROUP_CONCAT(r.display_name SEPARATOR ', ') as roles
  FROM users u 
  LEFT JOIN employees e ON u.employee_id = e.id 
  LEFT JOIN user_roles ur ON u.id = ur.user_id
  LEFT JOIN roles r ON ur.role_id = r.id
  GROUP BY u.id
  ORDER BY u.username
";
$users_result = $mysqli->query($users_query);
$users = $users_result->fetch_all(MYSQLI_ASSOC);

// Get unique departments for filtering
$departments_query = "SELECT DISTINCT department FROM employees WHERE department IS NOT NULL AND department != '' ORDER BY department";
$departments_result = $mysqli->query($departments_query);
$departments = $departments_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>User Accounts - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">User Accounts</h1>
          <p class="page-subtitle">Manage user credentials and access permissions</p>
        </div>
        <div class="d-flex gap-3">
          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.view')): ?>
            <a href="<?= appUrl('employees/list.php') ?>" class="btn btn-outline-primary">
              <i class="bi bi-people me-2"></i>
              View Employees
            </a>
          <?php endif; ?>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Filters and Search -->
    <div class="content-card">
      <div class="row align-items-end">
        <div class="col-md-4">
          <label for="searchInput" class="form-label">
            <i class="bi bi-search me-2"></i>
            Search Users
          </label>
          <input
            type="text"
            class="form-control"
            id="searchInput"
            placeholder="Search by username, employee name, or email..."
            onkeyup="filterUsers()"
          />
        </div>
        <div class="col-md-3">
          <label for="statusFilter" class="form-label">
            <i class="bi bi-funnel me-2"></i>
            Filter by Status
          </label>
          <select class="form-select" id="statusFilter" onchange="filterUsers()">
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>
        </div>
        <div class="col-md-3">
          <label for="departmentFilter" class="form-label">
            <i class="bi bi-building me-2"></i>
            Filter by Department
          </label>
          <select class="form-select" id="departmentFilter" onchange="filterUsers()">
            <option value="">All Departments</option>
            <?php foreach ($departments as $dept): ?>
              <option value="<?= strtolower(htmlspecialchars($dept['department'])) ?>">
                <?= htmlspecialchars($dept['department']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
        <div class="col-md-2">
          <div class="text-center">
            <small class="text-muted">Total Users</small>
            <div class="fw-bold fs-4" id="userCount"><?= count($users) ?></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Users List -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-people me-2"></i>
        User Accounts
      </h2>

      <?php if (count($users) === 0): ?>
        <div class="text-center py-5">
          <i class="bi bi-person-x" style="font-size: 3rem; color: var(--text-secondary);"></i>
          <p class="mt-3 text-muted">No user accounts found.</p>
          <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'employees.view')): ?>
            <a href="<?= appUrl('employees/list.php') ?>" class="btn btn-primary">
              <i class="bi bi-people me-2"></i>
              Manage Employees
            </a>
          <?php endif; ?>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table" id="userTable">
            <thead>
              <tr>
                <th>
                  <i class="bi bi-person me-2"></i>
                  User Account
                </th>
                <th>
                  <i class="bi bi-person-badge me-2"></i>
                  Employee Details
                </th>
                <th>
                  <i class="bi bi-shield-lock me-2"></i>
                  Roles & Status
                </th>
                <th width="150">
                  <i class="bi bi-gear me-2"></i>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($users as $user): ?>
                <tr class="user-row"
                    data-username="<?= strtolower(htmlspecialchars($user['username'])) ?>"
                    data-employee="<?= strtolower(htmlspecialchars($user['employee_name'] ?? '')) ?>"
                    data-email="<?= strtolower(htmlspecialchars($user['employee_email'] ?? '')) ?>"
                    data-department="<?= strtolower(htmlspecialchars($user['department'] ?? '')) ?>"
                    data-status="<?= strtolower($user['status']) ?>">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar me-3">
                        <?= strtoupper(substr($user['username'], 0, 1)) ?>
                      </div>
                      <div>
                        <strong><?= htmlspecialchars($user['username']) ?></strong>
                        <br>
                        <small class="text-muted">ID: <?= $user['id'] ?></small>
                        <br>
                        <small class="text-muted">
                          Created: <?= date('M j, Y', strtotime($user['created_at'])) ?>
                        </small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <?php if ($user['employee_name']): ?>
                      <strong><?= htmlspecialchars($user['employee_name']) ?></strong>
                      <br>
                      <?php if ($user['employee_email']): ?>
                        <small class="text-muted">
                          <i class="bi bi-envelope me-1"></i>
                          <?= htmlspecialchars($user['employee_email']) ?>
                        </small>
                        <br>
                      <?php endif; ?>
                      <?php if ($user['department']): ?>
                        <span class="badge bg-secondary"><?= htmlspecialchars($user['department']) ?></span>
                      <?php endif; ?>
                      <?php if ($user['position']): ?>
                        <br>
                        <small class="text-muted"><?= htmlspecialchars($user['position']) ?></small>
                      <?php endif; ?>
                    <?php else: ?>
                      <span class="text-muted">No employee linked</span>
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php
                    $status_class = match($user['status']) {
                      'active' => 'bg-success',
                      'inactive' => 'bg-warning',
                      'suspended' => 'bg-danger',
                      default => 'bg-secondary'
                    };
                    ?>
                    <span class="badge <?= $status_class ?> mb-2">
                      <?= ucfirst($user['status']) ?>
                    </span>
                    <br>
                    <?php if ($user['roles']): ?>
                      <small class="text-muted">
                        <strong>Roles:</strong> <?= htmlspecialchars($user['roles']) ?>
                      </small>
                    <?php else: ?>
                      <small class="text-muted">No roles assigned</small>
                    <?php endif; ?>
                  </td>
                  <td>
                    <div class="btn-group-vertical" role="group">
                      <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'users.edit')): ?>
                        <a href="edit.php?id=<?= $user['id'] ?>" class="btn btn-outline-primary btn-sm">
                          <i class="bi bi-pencil me-1"></i>
                          Edit
                        </a>
                      <?php endif; ?>

                      <?php if ($user['employee_id'] && hasPermission($mysqli, $_SESSION['user_id'], 'employees.view')): ?>
                        <a href="<?= appUrl('employees/edit.php?id=' . $user['employee_id']) ?>" class="btn btn-outline-info btn-sm">
                          <i class="bi bi-person me-1"></i>
                          View Employee
                        </a>
                      <?php endif; ?>

                      <?php if (hasPermission($mysqli, $_SESSION['user_id'], 'users.delete') && $user['id'] !== $_SESSION['user_id']): ?>
                        <form
                          method="POST"
                          onsubmit="return confirm('Are you sure you want to delete user <?= htmlspecialchars($user['username']) ?>? This action cannot be undone.');"
                          style="display:inline;"
                        >
                          <input type="hidden" name="user_id" value="<?= $user['id'] ?>" />
                          <button type="submit" name="delete_user" class="btn btn-outline-danger btn-sm">
                            <i class="bi bi-trash me-1"></i>
                            Delete
                          </button>
                        </form>
                      <?php endif; ?>
                    </div>
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <style>
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .hidden {
      display: none !important;
    }

    @media (max-width: 768px) {
      .table-container {
        overflow-x: auto;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    function filterUsers() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
      const departmentFilter = document.getElementById('departmentFilter').value.toLowerCase();
      
      const rows = document.querySelectorAll('.user-row');
      let visibleCount = 0;

      rows.forEach(row => {
        const username = row.dataset.username || '';
        const employee = row.dataset.employee || '';
        const email = row.dataset.email || '';
        const department = row.dataset.department || '';
        const status = row.dataset.status || '';

        const matchesSearch = !searchTerm ||
          username.includes(searchTerm) ||
          employee.includes(searchTerm) ||
          email.includes(searchTerm);

        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesDepartment = !departmentFilter || department === departmentFilter;

        if (matchesSearch && matchesStatus && matchesDepartment) {
          row.classList.remove('hidden');
          visibleCount++;
        } else {
          row.classList.add('hidden');
        }
      });

      // Update count
      document.getElementById('userCount').textContent = visibleCount;
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      // Add any initialization code here
    });
  </script>
</body>
</html>
