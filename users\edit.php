<?php
session_start();

// Include configuration and permissions system
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/permissions.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

// Check permissions - require user edit permissions
requirePermission($mysqli, 'users.edit');

$msg = "";
$error = "";
$user_id = intval($_GET['id'] ?? 0);

if (!$user_id) {
  header('Location: ' . appUrl('users/list.php'));
  exit;
}

// Fetch user data with employee information
$stmt = $mysqli->prepare("
  SELECT u.*, e.name as employee_name, e.email as employee_email, e.department, e.position
  FROM users u
  LEFT JOIN employees e ON u.employee_id = e.id
  WHERE u.id = ?
");
$stmt->bind_param('i', $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

// Set default status if not exists in database
if (!isset($user['status'])) {
  $user['status'] = 'active'; // Default status
}

if (!$user) {
  header('Location: ' . appUrl('users/list.php'));
  exit;
}

// Fetch user roles
$user_roles = getUserRoles($mysqli, $user_id);

// Ensure user_roles is always an array
if (!is_array($user_roles)) {
  $user_roles = []; // Fallback to empty array if not array
}

$current_role_ids = array_column($user_roles, 'id');

// Fetch all available roles
$roles_stmt = $mysqli->prepare("SELECT * FROM roles ORDER BY name");
$roles_stmt->execute();
$all_roles = $roles_stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$roles_stmt->close();

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['update_user'])) {
    $username = trim($_POST['username'] ?? '');
    $new_password = trim($_POST['new_password'] ?? '');
    $confirm_password = trim($_POST['confirm_password'] ?? '');
    $assigned_roles = $_POST['roles'] ?? [];

    if ($username) {
      try {
        // Check if username is unique (excluding current user)
        $username_check = $mysqli->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
        $username_check->bind_param('si', $username, $user_id);
        $username_check->execute();
        $username_result = $username_check->get_result();

        if ($username_result->num_rows > 0) {
          $error = "Username is already in use by another user.";
        }
        $username_check->close();

        // Validate password if provided
        if ($new_password && $new_password !== $confirm_password) {
          $error = "Password confirmation does not match.";
        }

        if (!$error) {
          $mysqli->begin_transaction();

          // Update user basic info (without status field since it doesn't exist in current schema)
          if ($new_password) {
            $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
            $update_stmt = $mysqli->prepare("UPDATE users SET username = ?, password_hash = ? WHERE id = ?");
            $update_stmt->bind_param('ssi', $username, $password_hash, $user_id);
          } else {
            $update_stmt = $mysqli->prepare("UPDATE users SET username = ? WHERE id = ?");
            $update_stmt->bind_param('si', $username, $user_id);
          }

          if ($update_stmt->execute()) {
            $update_stmt->close();

            // Update user roles
            // First, remove all current roles
            $remove_roles_stmt = $mysqli->prepare("DELETE FROM user_roles WHERE user_id = ?");
            $remove_roles_stmt->bind_param('i', $user_id);
            $remove_roles_stmt->execute();
            $remove_roles_stmt->close();

            // Then add new roles
            if (!empty($assigned_roles)) {
              foreach ($assigned_roles as $role_id) {
                assignRoleToUser($mysqli, $user_id, intval($role_id), $_SESSION['user_id']);
              }
            }

            $mysqli->commit();
            $msg = "User account updated successfully.";

            // Refresh user data
            $refresh_stmt = $mysqli->prepare("
              SELECT u.*, e.name as employee_name, e.email as employee_email, e.department, e.position
              FROM users u
              LEFT JOIN employees e ON u.employee_id = e.id
              WHERE u.id = ?
            ");
            $refresh_stmt->bind_param('i', $user_id);
            $refresh_stmt->execute();
            $refresh_result = $refresh_stmt->get_result();
            $user = $refresh_result->fetch_assoc();
            $refresh_stmt->close();

            // Set default status if not exists in database
            if (!isset($user['status'])) {
              $user['status'] = 'active'; // Default status
            }

            // Refresh roles
            $user_roles = getUserRoles($mysqli, $user_id);

            // Ensure user_roles is always an array
            if (!is_array($user_roles)) {
              $user_roles = []; // Fallback to empty array if not array
            }

            $current_role_ids = array_column($user_roles, 'id');
          } else {
            throw new Exception("Failed to update user account.");
          }
        }
      } catch (Exception $e) {
        $mysqli->rollback();
        $error = "Error updating user: " . $e->getMessage();
      }
    } else {
      $error = "Username is required.";
    }
  }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Edit User Account - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="../style.css" />
</head>
<body>
  <?php include '../includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Edit User Account</h1>
          <p class="page-subtitle">Manage user credentials and permissions</p>
        </div>
        <div class="d-flex gap-3">
          <a href="<?= appUrl('users/list.php') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            Back to User List
          </a>
          <?php if ($user['employee_id']): ?>
            <a href="<?= appUrl('employees/edit.php?id=' . $user['employee_id']) ?>" class="btn btn-outline-primary">
              <i class="bi bi-person me-2"></i>
              View Employee
            </a>
          <?php endif; ?>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- User Overview -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-person-circle me-2"></i>
        User Overview
      </h2>
      <div class="row">
        <div class="col-md-3">
          <div class="d-flex align-items-center">
            <div class="avatar me-3" style="width: 60px; height: 60px; font-size: 1.5rem;">
              <?= strtoupper(substr($user['username'], 0, 1)) ?>
            </div>
            <div>
              <h5 class="mb-1"><?= htmlspecialchars($user['username']) ?></h5>
              <small class="text-muted">User ID: <?= $user['id'] ?></small>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <small class="text-muted">Employee</small>
          <div class="fw-semibold">
            <?= $user['employee_name'] ? htmlspecialchars($user['employee_name']) : 'No Employee Linked' ?>
          </div>
          <?php if ($user['department']): ?>
            <small class="text-muted"><?= htmlspecialchars($user['department']) ?></small>
          <?php endif; ?>
        </div>
        <div class="col-md-3">
          <small class="text-muted">Account Type</small>
          <div>
            <span class="badge bg-primary">
              User Account
            </span>
          </div>
        </div>
        <div class="col-md-3">
          <small class="text-muted">Current Roles</small>
          <div>
            <?php if (!empty($user_roles)): ?>
              <?php foreach ($user_roles as $role): ?>
                <span class="badge bg-primary me-1"><?= htmlspecialchars($role['display_name']) ?></span>
              <?php endforeach; ?>
            <?php else: ?>
              <span class="text-muted">No roles assigned</span>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit User Form -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-gear me-2"></i>
        Account Settings
      </h2>
      <form method="POST" id="userForm">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label for="username" class="form-label">
                <i class="bi bi-person me-2"></i>
                Username *
              </label>
              <input
                type="text"
                class="form-control"
                name="username"
                id="username"
                required
                placeholder="Enter username"
                value="<?= htmlspecialchars($user['username']) ?>"
              />
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="new_password" class="form-label">
                <i class="bi bi-lock me-2"></i>
                New Password
              </label>
              <input
                type="password"
                class="form-control"
                name="new_password"
                id="new_password"
                placeholder="Leave blank to keep current password"
              />
              <small class="text-muted">Leave blank to keep current password</small>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="confirm_password" class="form-label">
                <i class="bi bi-lock-fill me-2"></i>
                Confirm New Password
              </label>
              <input
                type="password"
                class="form-control"
                name="confirm_password"
                id="confirm_password"
                placeholder="Confirm new password"
              />
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-shield-lock me-2"></i>
            User Roles
          </label>
          <div class="row">
            <?php foreach ($all_roles as $role): ?>
              <div class="col-md-4 mb-2">
                <div class="form-check">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    name="roles[]"
                    value="<?= $role['id'] ?>"
                    id="role_<?= $role['id'] ?>"
                    <?= in_array($role['id'], $current_role_ids) ? 'checked' : '' ?>
                  />
                  <label class="form-check-label" for="role_<?= $role['id'] ?>">
                    <strong><?= htmlspecialchars($role['display_name']) ?></strong>
                    <?php if ($role['description']): ?>
                      <br><small class="text-muted"><?= htmlspecialchars($role['description']) ?></small>
                    <?php endif; ?>
                  </label>
                </div>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

        <div class="d-flex gap-3 mt-4">
          <button type="submit" name="update_user" class="btn btn-primary">
            <i class="bi bi-check-circle me-2"></i>
            Update User Account
          </button>
          <a href="<?= appUrl('users/list.php') ?>" class="btn btn-secondary">
            <i class="bi bi-x-circle me-2"></i>
            Cancel
          </a>
        </div>
      </form>
    </div>
  </div>

  <style>
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    @media (max-width: 768px) {
      .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
      }

      .d-flex.gap-3 .btn {
        width: 100%;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Form validation
    document.getElementById('userForm').addEventListener('submit', function(e) {
      const username = document.getElementById('username').value.trim();
      const newPassword = document.getElementById('new_password').value;
      const confirmPassword = document.getElementById('confirm_password').value;

      if (!username) {
        e.preventDefault();
        alert('Username is required.');
        document.getElementById('username').focus();
        return false;
      }

      if (newPassword && newPassword !== confirmPassword) {
        e.preventDefault();
        alert('Password confirmation does not match.');
        document.getElementById('confirm_password').focus();
        return false;
      }
    });

    // Additional form enhancements can be added here
  </script>
</body>
</html>
